import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5176,
    allowedHosts: 'all', // 允许所有主机
    // 或者可以指定特定主机
    // allowedHosts: [
    //   'localhost',
    //   '.trycloudflare.com',
    //   '.ngrok.io'
    // ]
  },
})
