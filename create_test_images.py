#!/usr/bin/env python3
"""
创建测试图片用于验证图片间距功能
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_images():
    """创建测试图片"""
    
    # 确保目录存在
    os.makedirs('test_images', exist_ok=True)
    
    # 创建第一张测试图片 - 蓝色背景
    def create_sample1():
        width, height = 150, 100
        img = Image.new('RGB', (width, height), '#4A90E2')
        draw = ImageDraw.Draw(img)
        
        # 绘制边框
        draw.rectangle([(5, 5), (width-5, height-5)], outline='white', width=3)
        
        # 添加文字
        try:
            font = ImageFont.load_default()
            text = "Sample 1"
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            draw.text((x, y), text, fill='white', font=font)
        except:
            # 简化版本
            draw.text((50, 40), "Sample 1", fill='white')
        
        img.save('test_images/sample1.png')
        print("✅ 创建测试图片1: test_images/sample1.png")
    
    # 创建第二张测试图片 - 绿色背景
    def create_sample2():
        width, height = 150, 100
        img = Image.new('RGB', (width, height), '#7ED321')
        draw = ImageDraw.Draw(img)
        
        # 绘制边框
        draw.rectangle([(5, 5), (width-5, height-5)], outline='white', width=3)
        
        # 添加文字
        try:
            font = ImageFont.load_default()
            text = "Sample 2"
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            draw.text((x, y), text, fill='white', font=font)
        except:
            # 简化版本
            draw.text((50, 40), "Sample 2", fill='white')
        
        img.save('test_images/sample2.png')
        print("✅ 创建测试图片2: test_images/sample2.png")
    
    # 创建第三张测试图片 - 橙色背景
    def create_sample3():
        width, height = 150, 100
        img = Image.new('RGB', (width, height), '#F5A623')
        draw = ImageDraw.Draw(img)
        
        # 绘制边框
        draw.rectangle([(5, 5), (width-5, height-5)], outline='white', width=3)
        
        # 添加文字
        try:
            font = ImageFont.load_default()
            text = "Sample 3"
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            draw.text((x, y), text, fill='white', font=font)
        except:
            # 简化版本
            draw.text((50, 40), "Sample 3", fill='white')
        
        img.save('test_images/sample3.png')
        print("✅ 创建测试图片3: test_images/sample3.png")
    
    # 创建所有测试图片
    create_sample1()
    create_sample2()
    create_sample3()
    
    print("\n🎉 所有测试图片创建完成！")
    print("现在可以使用 test_image_spacing.md 文档来测试图片间距功能。")

if __name__ == "__main__":
    create_test_images()
