# 图片间距调整指南

## 📖 概述

PrintMind 支持调整同一行多张图片之间的间距，让你可以根据需要创建紧凑或宽松的图片布局。

## 🎛️ 如何调整图片间距

### 1. 打开配置面板
- 点击右上角的 **⚙️ 设置** 按钮
- 配置面板会在右侧展开

### 2. 找到图片间距设置
- 在配置面板中找到 **"内容设置"** 部分
- 找到 **"图片间距 (px)"** 选项

### 3. 调整间距值
- **范围**：5-50像素
- **默认值**：20像素
- **建议值**：
  - **紧凑布局**：5-10像素
  - **标准布局**：15-25像素
  - **宽松布局**：30-50像素

## 🖼️ 影响范围

图片间距设置会影响以下两种情况：

### 1. 普通文档中的并排图片
```markdown
![图片1](image1.png?size=small)
![图片2](image2.png?size=small)
![图片3](image3.png?size=small)
```

### 2. 答案框中的并排图片
```markdown
/答案：这里是答案内容。

![答案图1](image1.png?size=small)
![答案图2](image2.png?size=small)

解析：这里是解析内容。

![解析图1](image1.png?size=small)
![解析图2](image2.png?size=small)/
```

## 📏 间距效果对比

### 5像素间距（紧凑）
- 图片之间几乎无缝连接
- 适合需要紧密排列的图片组合
- 节省页面空间

### 20像素间距（标准）
- 图片之间有适中的分隔
- 视觉平衡，易于区分
- 推荐的默认设置

### 50像素间距（宽松）
- 图片之间有明显的分隔
- 突出每张图片的独立性
- 适合重要图片的展示

## 🔧 技术实现

### 后端实现
- 在 `LayoutConfig` 模型中添加了 `image_spacing` 字段
- PDF服务中的图片布局函数支持动态间距配置
- 同时支持普通文档和答案框中的图片间距调整

### 前端实现
- 配置面板中添加了图片间距控制组件
- 实时预览间距调整效果
- 配置值自动保存和同步

## 💡 使用技巧

### 1. 根据图片尺寸调整
- **小图片**：可以使用较小间距（5-15px）
- **中等图片**：使用标准间距（15-25px）
- **大图片**：使用较大间距（25-50px）

### 2. 根据内容类型调整
- **步骤说明图**：使用较小间距，体现连续性
- **对比图片**：使用标准间距，便于比较
- **独立展示图**：使用较大间距，突出重点

### 3. 根据页面布局调整
- **紧凑页面**：使用较小间距节省空间
- **标准页面**：使用默认间距保持平衡
- **宽松页面**：使用较大间距提升视觉效果

## 🎯 最佳实践

1. **保持一致性**：在同一文档中使用相同的间距设置
2. **考虑打印效果**：较小间距在打印时可能显得过于紧密
3. **测试不同设备**：在不同屏幕尺寸上预览效果
4. **结合图片尺寸**：间距应与图片大小成比例

## 🔄 实时预览

调整图片间距后，PDF预览会立即更新，你可以：
- 实时查看间距效果
- 对比不同间距设置
- 找到最适合的间距值

## 📝 注意事项

- 间距设置对单张图片无效，只影响并排显示的多张图片
- 图片必须连续出现（中间只能有空行）才会并排显示
- 每行最多显示3张图片，超出会自动换行
- 间距设置会同时影响文档正文和答案框中的图片
